services:
    php-fpm:
        image: docker.io/bitnami/php-fpm:8.4
        ports:
            - 9000:9000
        volumes:
            - .:/app
            - ./docker/php.ini:/opt/bitnami/php/etc/php.ini
        container_name: ${APP_NAME}-php-fpm
        networks:
            - app-network

    workspace:
        tty: true
        image: docker.io/bitnami/laravel:latest
        environment:
            - LARAVEL_DATABASE_TYPE=${DB_CONNECTION}
            - LARAVEL_DATABASE_HOST=${DB_HOST}
            - LARAVEL_DATABASE_NAME=${DB_DATABASE}
            - LARAVEL_DATABASE_USER=${DB_USERNAME}
            - LARAVEL_DATABASE_PASSWORD=${DB_PASSWORD}
            - LARAVEL_SKIP_COMPOSER_UPDATE=yes
        volumes:
            - .:/app
            - ./docker/php.ini:/opt/bitnami/php/etc/php.ini
        container_name: ${APP_NAME}-workspace
        networks:
            - app-network

    postgresql:
        image: docker.io/bitnami/postgresql:17
        ports:
            - '5432:5432'
        environment:
            - POSTGRESQL_USERNAME=${DB_USERNAME}
            - POSTGRESQL_PASSWORD=${DB_PASSWORD}
            - POSTGRESQL_DATABASE=${DB_DATABASE}
        volumes:
            - 'postgresql_data:/bitnami/postgresql'
        container_name: ${APP_NAME}-postgresql
        networks:
            - app-network

    nginx:
        image: docker.io/bitnami/nginx:1.28
        ports:
            - '80:8080'
        container_name: ${APP_NAME}-nginx
        restart: unless-stopped
        volumes:
            - .:/app
            - ./docker/nginx/laravel.conf:/opt/bitnami/nginx/conf/server_blocks/laravel.conf:ro
        networks:
            - app-network

volumes:
    postgresql_data:
        driver: local

networks:
    app-network:
